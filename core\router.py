"""
Routing logic for mapping document type + extracted data to upload folder and extractor.
"""

from typing import Dict, Any
from datetime import datetime
import re


def _extract_document_year(extracted_fields: Dict[str, Any], fallback_year: int) -> int:
    """Extract the document year from ChatGPT's analysis.

    ChatGPT is instructed to extract 'document_year' as the most contextually
    relevant year for the document. Falls back to current year if not found.
    """
    # First, check if ChatGPT extracted document_year directly
    if "document_year" in extracted_fields:
        doc_year = extracted_fields["document_year"]
        if doc_year:
            # Try to parse as integer or extract from string
            try:
                if isinstance(doc_year, int):
                    return doc_year
                year = _parse_year_from_string(str(doc_year))
                if year:
                    return year
            except (ValueError, TypeError):
                pass

    # Fallback: try common date fields as backup
    date_fields = [
        "signing_date", "signed_date", "document_date", "issue_date",
        "manufacturing_date", "test_date", "analysis_date", "created_date",
        "date", "year", "expiry_date", "creation_date"
    ]

    for field_name in date_fields:
        if field_name in extracted_fields:
            date_value = extracted_fields[field_name]
            if date_value:
                year = _parse_year_from_string(str(date_value))
                if year:
                    print(f"⚠️ Document year extracted from fallback field {field_name}: {year}")
                    return year

    print(f"⚠️ No document year found in extracted fields, using fallback year: {fallback_year}")
    return fallback_year


def _parse_year_from_string(date_str: str) -> int | None:
    """Parse year from a date string using regex patterns."""
    if not date_str:
        return None

    # Look for 4-digit years (1900-2099)
    year_match = re.search(r'\b(19|20)\d{2}\b', date_str)
    if year_match:
        return int(year_match.group())

    return None


def resolve(
    doc_type: str,
    tenant_config: Dict[str, Any],
    extracted_fields: Dict[str, Any] = None,
) -> str:
    """Resolve the OneDrive upload folder for a document.

    This implementation supports the *new* ``config.json`` structure suggested
    by the user which contains two key sections:

    1. ``defaults.storage`` – tenant-wide defaults
    2. ``document_types.<type>.storage`` – optional per-document-type overrides

    The final folder path is composed as::

        <root_onedrive_folder>/<subfolder_format>

    with ``subfolder_format`` processed through ``str.format`` using the
    following placeholders:

    * ``{doc_type}``       – the *raw* ``doc_type`` string provided
    * ``{yyyy}``           – current 4-digit year
    * ``{mm}``             – current 2-digit month (01-12)
    * ``{document_year}``  – year extracted from document (e.g., signing date)
    * ``{company_name}``   – company name extracted from document
    * ``{supplier}``       – supplier name extracted from document

    If extracted_fields is provided, document-specific data will be used for
    placeholders like {company_name}, {document_year}, etc. If not available,
    current date will be used as fallback for year-based placeholders.
    """
    # ------------------------------------------------------------------
    # Gather defaults + overrides
    # ------------------------------------------------------------------
    defaults: Dict[str, Any] = tenant_config.get("defaults", {})
    storage_defaults: Dict[str, Any] = defaults.get("storage", {})

    doc_rules: Dict[str, Any] = tenant_config.get("document_types", {}).get(doc_type, {})
    storage_overrides: Dict[str, Any] = doc_rules.get("storage", {})

    root = storage_overrides.get(
        "root_onedrive_folder",
        storage_defaults.get("root_onedrive_folder", ""),
    )
    subfmt = storage_overrides.get(
        "subfolder_format",
        storage_defaults.get("subfolder_format", "{doc_type}"),
    )

    # ------------------------------------------------------------------
    # Placeholder substitution
    # ------------------------------------------------------------------
    now = datetime.now()

    # Prepare format parameters
    format_params = {
        "doc_type": doc_type,
        "yyyy": f"{now.year:04d}",
        "mm": f"{now.month:02d}",
    }

    # Add extracted field parameters if available
    if extracted_fields:
        # Extract company name (try various field names)
        company_name = (
            extracted_fields.get("company_name") or
            extracted_fields.get("supplier") or
            extracted_fields.get("manufacturer") or
            extracted_fields.get("vendor") or
            "Unknown"
        )
        format_params["company_name"] = company_name
        format_params["supplier"] = company_name  # Alias for backward compatibility

        # Extract document year (try various date fields)
        document_year = _extract_document_year(extracted_fields, now.year)
        format_params["document_year"] = f"{document_year:04d}"

    try:
        subfolder = subfmt.format(**format_params)
    except KeyError as e:
        # Unknown placeholder → keep format string as-is to avoid crashing
        print(f"⚠️ Unknown placeholder in subfolder_format: {e}")
        subfolder = subfmt

    # Normalise separators and return
    subfolder = subfolder.strip("/\\")
    root = root.strip("/\\")
    if root and subfolder:
        return f"{root}/{subfolder}"
    return subfolder or root

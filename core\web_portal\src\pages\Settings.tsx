import React, { useState, useEffect } from 'react';
import { User, Mail, Bell, CreditCard, Key, Trash2, Plus, Edit3, FileText, FolderOpen, Settings as SettingsIcon, X } from 'lucide-react';
import Layout from '../components/common/Layout';
import Card from '../components/common/Card';
import { useAuth } from '../contexts/AuthContext';
import { mockMailboxes } from '../data/mockData';
import toast from 'react-hot-toast';

const Settings: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'mailboxes' | 'documents' | 'notifications' | 'billing'>('profile');
  const [mailboxes, setMailboxes] = useState(mockMailboxes);
  const [showAddMailbox, setShowAddMailbox] = useState(false);
  const [showEditMailbox, setShowEditMailbox] = useState(false);
  const [editingMailbox, setEditingMailbox] = useState<any>(null);
  const [configData, setConfigData] = useState<any>(null);
  const [isDevelopmentMode] = useState(true); // For now, assume development mode

  // Global Storage Settings state
  const [globalSettings, setGlobalSettings] = useState({
    upload: true,
    notify: false,
    baseStoragePath: '{doc_type}/{document_year}/{company_name}',
    notificationRecipients: [{ name: '', email: '' }],
    preferredLanguage: 'English',
    processExternalOnly: true,
    companyDomains: ['company.com']
  });

  // Document Types state
  const [showAddDocType, setShowAddDocType] = useState(false);
  const [showEditDocType, setShowEditDocType] = useState(false);
  const [editingDocType, setEditingDocType] = useState<any>(null);
  const [documentTypes, setDocumentTypes] = useState<any>({});
  const [lastAutoSave, setLastAutoSave] = useState<Date | null>(null);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const fetchConfigData = async () => {
    try {
      const response = await fetch(`/api/config/load?dev_mode=${isDevelopmentMode}`);
      if (response.ok) {
        const config = await response.json();
        setConfigData(config);
      } else {
        console.error('Failed to load config data');
      }
    } catch (error) {
      console.error('Error fetching config data:', error);
    }
  };

  useEffect(() => {
    fetchConfigData();
  }, [isDevelopmentMode]);

  // Update global settings and document types when config data is loaded
  useEffect(() => {
    if (configData?.defaults) {
      const defaults = configData.defaults;
      setGlobalSettings(prev => ({
        ...prev,
        upload: defaults.actions?.upload || true,
        notify: defaults.actions?.notify || false,
        baseStoragePath: defaults.storage?.subfolder_format || '{doc_type}/{document_year}/{company_name}',
        notificationRecipients: defaults.notification?.recipients || [{ name: '', email: '' }],
        preferredLanguage: defaults.preferred_language || 'English',
        processExternalOnly: defaults.email_filtering?.process_external_only || true,
        companyDomains: defaults.email_filtering?.company_domains || ['company.com']
      }));
    }

    if (configData?.document_types) {
      setDocumentTypes(configData.document_types);
    }
  }, [configData]);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: <User className="h-4 w-4" /> },
    { id: 'mailboxes', label: 'Mailboxes', icon: <Mail className="h-4 w-4" /> },
    { id: 'documents', label: 'Document Types & Storage', icon: <FileText className="h-4 w-4" /> },
    { id: 'notifications', label: 'Notifications', icon: <Bell className="h-4 w-4" /> },
    { id: 'billing', label: 'Billing', icon: <CreditCard className="h-4 w-4" /> }
  ];

  const handleToggleMailbox = (id: string) => {
    setMailboxes(prev => prev.map(mailbox =>
      mailbox.id === id ? { ...mailbox, isActive: !mailbox.isActive } : mailbox
    ));
    toast.success('Mailbox status updated');
  };

  const handleAddMailbox = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const newMailbox = {
      id: `mb-${Date.now()}`,
      email: formData.get('email') as string,
      name: formData.get('name') as string,
      isActive: true,
      documentTypes: [], // Document types are now defined globally
      processedCount: 0
    };

    setMailboxes(prev => [...prev, newMailbox]);
    setShowAddMailbox(false);
    toast.success('Mailbox added successfully');
  };

  // TODO: Replace with API calls when integrating with real customer data
  const handleEditMailbox = (mailbox: any) => {
    setEditingMailbox({ ...mailbox });
    setShowEditMailbox(true);
  };

  const handleUpdateMailbox = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const updatedMailbox = {
      ...editingMailbox,
      email: formData.get('email') as string,
      name: formData.get('name') as string,
    };

    // TODO: Replace with API call to update mailbox in tenant configuration
    setMailboxes(prev => prev.map(mailbox =>
      mailbox.id === editingMailbox.id ? updatedMailbox : mailbox
    ));
    setShowEditMailbox(false);
    setEditingMailbox(null);
    toast.success('Mailbox updated successfully');
  };

  const handleRemoveMailbox = (id: string) => {
    if (window.confirm('Are you sure you want to remove this mailbox? This action cannot be undone.')) {
      // TODO: Replace with API call to remove mailbox from tenant configuration
      setMailboxes(prev => prev.filter(mailbox => mailbox.id !== id));
      toast.success('Mailbox removed successfully');
    }
  };

  // Global Settings handlers
  const handleGlobalSettingsChange = (field: string, value: any) => {
    setGlobalSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddRecipient = () => {
    setGlobalSettings(prev => ({
      ...prev,
      notificationRecipients: [...prev.notificationRecipients, { name: '', email: '' }]
    }));
  };

  const handleRemoveRecipient = (index: number) => {
    setGlobalSettings(prev => ({
      ...prev,
      notificationRecipients: prev.notificationRecipients.filter((_, i) => i !== index)
    }));
  };

  const handleRecipientChange = (index: number, field: 'name' | 'email', value: string) => {
    setGlobalSettings(prev => ({
      ...prev,
      notificationRecipients: prev.notificationRecipients.map((recipient, i) =>
        i === index ? { ...recipient, [field]: value } : recipient
      )
    }));
  };

  const handleSaveGlobalSettings = async () => {
    try {
      const updatedConfig = {
        ...configData,
        defaults: {
          ...configData?.defaults,
          actions: {
            upload: globalSettings.upload,
            notify: globalSettings.notify
          },
          storage: {
            subfolder_format: globalSettings.baseStoragePath
          },
          notification: {
            recipients: globalSettings.notificationRecipients.filter(r => r.email && r.name)
          },
          preferred_language: globalSettings.preferredLanguage,
          email_filtering: {
            process_external_only: globalSettings.processExternalOnly,
            company_domains: globalSettings.companyDomains
          }
        }
      };

      const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedConfig),
      });

      if (response.ok) {
        toast.success('Global settings saved successfully');
        fetchConfigData(); // Refresh config data
      } else {
        const error = await response.json();
        toast.error(`Failed to save settings: ${error.error}`);
      }
    } catch (error) {
      console.error('Error saving global settings:', error);
      toast.error('Failed to save global settings');
    }
  };

  // Document Type handlers
  const handleAddDocType = () => {
    setShowAddDocType(true);
  };

  const handleEditDocType = (docType: string, config: any) => {
    setEditingDocType({
      name: docType,
      originalName: docType, // Keep track of original name for updates
      keywords: config.keywords?.join(', ') || '',
      upload: config.actions?.upload || false,
      notify: config.actions?.notify || false,
      storagePath: config.storage?.subfolder_format || `${docType}/{document_year}/{company_name}`,
      recipients: config.notification?.recipients || [{ name: '', email: '' }],
      preferredLanguage: config.preferred_language || globalSettings.preferredLanguage
    });
    setShowEditDocType(true);
  };

  const handleUpdateDocType = async (updatedDocTypeData: any) => {
    try {
      const { originalName, name, ...docTypeConfig } = updatedDocTypeData;

      const updatedDocTypes = { ...configData.document_types };

      // If name changed, remove old entry and add new one
      if (originalName !== name) {
        delete updatedDocTypes[originalName];
      }

      updatedDocTypes[name] = {
        keywords: docTypeConfig.keywords.split(',').map((k: string) => k.trim()),
        actions: {
          upload: docTypeConfig.upload,
          notify: docTypeConfig.notify
        },
        ...(docTypeConfig.upload && docTypeConfig.storagePath && {
          storage: {
            subfolder_format: docTypeConfig.storagePath
          }
        }),
        ...(docTypeConfig.notify && docTypeConfig.recipients.some((r: any) => r.email) && {
          notification: {
            recipients: docTypeConfig.recipients.filter((r: any) => r.email && r.name)
          },
          preferred_language: docTypeConfig.preferredLanguage
        })
      };

      const updatedConfig = {
        ...configData,
        document_types: updatedDocTypes
      };

      await autoSaveConfig(updatedConfig);
      setShowEditDocType(false);
      setEditingDocType(null);
      toast.success(`Document type "${name}" updated successfully`);
    } catch (error) {
      console.error('Error updating document type:', error);
      toast.error('Failed to update document type');
    }
  };

  const handleDeleteDocType = async (docType: string) => {
    if (window.confirm(`Are you sure you want to delete the "${docType}" document type? This action cannot be undone.`)) {
      try {
        const updatedDocTypes = { ...configData.document_types };
        delete updatedDocTypes[docType];

        const updatedConfig = {
          ...configData,
          document_types: updatedDocTypes
        };

        await autoSaveConfig(updatedConfig);
        toast.success(`Document type "${docType}" deleted successfully`);
      } catch (error) {
        console.error('Error deleting document type:', error);
        toast.error('Failed to delete document type');
      }
    }
  };

  // Auto-save function
  const autoSaveConfig = async (updatedConfig: any) => {
    setAutoSaveStatus('saving');
    try {
      const response = await fetch(`/api/config/save?dev_mode=${isDevelopmentMode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedConfig),
      });

      if (response.ok) {
        setConfigData(updatedConfig);
        setAutoSaveStatus('saved');
        setLastAutoSave(new Date());
        return true;
      } else {
        const error = await response.json();
        setAutoSaveStatus('error');
        throw new Error(error.error);
      }
    } catch (error) {
      console.error('Auto-save error:', error);
      setAutoSaveStatus('error');
      throw error;
    }
  };

  const handleDocTypeChange = async (docType: string, field: string, value: any) => {
    try {
      const updatedDocTypes = {
        ...configData.document_types,
        [docType]: {
          ...configData.document_types[docType],
          [field]: value
        }
      };

      const updatedConfig = {
        ...configData,
        document_types: updatedDocTypes
      };

      await autoSaveConfig(updatedConfig);
    } catch (error) {
      console.error('Error updating document type:', error);
      toast.error('Failed to save changes');
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your account and preferences</p>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card title="Personal Information" description="Update your personal details">
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    defaultValue={user?.name}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    defaultValue={user?.email}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company
                  </label>
                  <input
                    type="text"
                    placeholder="Your company name"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                  onClick={(e) => {
                    e.preventDefault();
                    toast.success('Profile updated successfully');
                  }}
                >
                  Save Changes
                </button>
              </form>
            </Card>

            <Card title="Security" description="Manage your account security">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Change Password</h4>
                  <form className="space-y-3">
                    <input
                      type="password"
                      placeholder="Current password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="password"
                      placeholder="New password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="password"
                      placeholder="Confirm new password"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                      onClick={(e) => {
                        e.preventDefault();
                        toast.success('Password changed successfully');
                      }}
                    >
                      Update Password
                    </button>
                  </form>
                </div>

                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">API Keys</h4>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Key className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">API Key</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">sk-••••••••••••••••••••••••••••••••</p>
                      </div>
                    </div>
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                      Regenerate
                    </button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'mailboxes' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Email Mailboxes</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Configure email addresses for document processing</p>
              </div>
              <button
                onClick={() => setShowAddMailbox(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Mailbox</span>
              </button>
            </div>

            {showAddMailbox && (
              <Card title="Add New Mailbox" description="Configure a new email address for document processing">
                <form onSubmit={handleAddMailbox} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        name="email"
                        type="email"
                        required
                        placeholder="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Display Name
                      </label>
                      <input
                        name="name"
                        type="text"
                        required
                        placeholder="Document Processing"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                    >
                      Add Mailbox
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowAddMailbox(false)}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </Card>
            )}

            {showEditMailbox && editingMailbox && (
              <Card title="Edit Mailbox" description="Update mailbox configuration">
                <form onSubmit={handleUpdateMailbox} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        name="email"
                        type="email"
                        required
                        defaultValue={editingMailbox.email}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Display Name
                      </label>
                      <input
                        name="name"
                        type="text"
                        required
                        defaultValue={editingMailbox.name}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                    >
                      Update Mailbox
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowEditMailbox(false);
                        setEditingMailbox(null);
                      }}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </Card>
            )}

            <div className="grid gap-4">
              {mailboxes.map((mailbox) => (
                <Card key={mailbox.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-lg ${mailbox.isActive ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                        <Mail className="h-6 w-6" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">{mailbox.name}</h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{mailbox.email}</p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {mailbox.processedCount.toLocaleString()} documents processed
                          </span>
                          {mailbox.lastProcessed && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Last processed: {new Date(mailbox.lastProcessed).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={mailbox.isActive}
                          onChange={() => handleToggleMailbox(mailbox.id)}
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
                      </label>
                      <button
                        onClick={() => handleEditMailbox(mailbox)}
                        className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                        title="Edit mailbox"
                      >
                        <Edit3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleRemoveMailbox(mailbox.id)}
                        className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-400"
                        title="Remove mailbox"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <Card title="Notification Preferences" description="Configure how you receive notifications">
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Email Notifications</h4>
                <div className="space-y-3">
                  {[
                    { id: 'processing', label: 'Document processing completed', description: 'Get notified when documents are successfully processed' },
                    { id: 'errors', label: 'Processing errors', description: 'Get notified when document processing fails' },
                    { id: 'limits', label: 'Usage limits', description: 'Get notified when approaching usage limits' },
                    { id: 'security', label: 'Security alerts', description: 'Get notified about security events' }
                  ].map((notification) => (
                    <div key={notification.id} className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        id={notification.id}
                        defaultChecked
                        className="form-checkbox h-4 w-4 text-blue-600 mt-1"
                      />
                      <div>
                        <label htmlFor={notification.id} className="text-sm font-medium text-gray-900 dark:text-white">
                          {notification.label}
                        </label>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{notification.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Frequency</h4>
                <div className="space-y-3">
                  {[
                    { value: 'instant', label: 'Instant', description: 'Receive notifications immediately' },
                    { value: 'daily', label: 'Daily Digest', description: 'Receive a daily summary of activities' },
                    { value: 'weekly', label: 'Weekly Summary', description: 'Receive a weekly summary of activities' }
                  ].map((frequency) => (
                    <div key={frequency.value} className="flex items-start space-x-3">
                      <input
                        type="radio"
                        id={frequency.value}
                        name="frequency"
                        value={frequency.value}
                        defaultChecked={frequency.value === 'instant'}
                        className="form-radio h-4 w-4 text-blue-600 mt-1"
                      />
                      <div>
                        <label htmlFor={frequency.value} className="text-sm font-medium text-gray-900 dark:text-white">
                          {frequency.label}
                        </label>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{frequency.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-6">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                  onClick={() => toast.success('Notification preferences updated')}
                >
                  Save Preferences
                </button>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'documents' && (
          <div className="space-y-6">
            {/* Global Storage Settings */}
            <Card title="Global Storage Settings" description="Configure default settings for general documents that don't match specific document types">
              <div className="space-y-6">
                {/* Processing Actions */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Default Actions for General Documents</h4>
                  <div className="space-y-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={globalSettings.upload}
                        onChange={(e) => handleGlobalSettingsChange('upload', e.target.checked)}
                        className="form-checkbox h-4 w-4 text-blue-600"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Upload documents</span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={globalSettings.notify}
                        onChange={(e) => handleGlobalSettingsChange('notify', e.target.checked)}
                        className="form-checkbox h-4 w-4 text-blue-600"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Send notifications</span>
                    </label>

                    <div>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={globalSettings.processExternalOnly}
                          onChange={(e) => handleGlobalSettingsChange('processExternalOnly', e.target.checked)}
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Process external emails only</span>
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                        When enabled, only emails from external senders will be processed. Internal company emails will be ignored.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Company Domains - Only show if Process External Only is enabled */}
                {globalSettings.processExternalOnly && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Company Domains</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Internal Company Domains (comma-separated)
                        </label>
                        <input
                          type="text"
                          value={globalSettings.companyDomains.join(', ')}
                          onChange={(e) => handleGlobalSettingsChange('companyDomains', e.target.value.split(',').map(d => d.trim()).filter(d => d))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="company.com, subsidiary.com"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Emails from these domains will be considered internal and ignored when "Process external emails only" is enabled.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Base Storage Path - Only show if Upload is enabled */}
                {globalSettings.upload && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Base Storage Path</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Storage Path Format
                        </label>
                        <input
                          type="text"
                          value={globalSettings.baseStoragePath}
                          onChange={(e) => handleGlobalSettingsChange('baseStoragePath', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          placeholder="{doc_type}/{document_year}/{company_name}"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Variables: {'{doc_type}'}, {'{document_year}'}, {'{company_name}'}
                        </p>
                      </div>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Preview for general documents:</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400 font-mono">
                          📁 {globalSettings.baseStoragePath.replace('{doc_type}', 'General').replace('{document_year}', '2024').replace('{company_name}', 'Unknown Company')}/
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notification Settings - Only show if Notify is enabled */}
                {globalSettings.notify && (
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Recipients</h4>
                      <div className="space-y-3">
                        {globalSettings.notificationRecipients.map((recipient, index) => (
                          <div key={index} className="flex items-center space-x-3">
                            <input
                              type="text"
                              placeholder="Name"
                              value={recipient.name}
                              onChange={(e) => handleRecipientChange(index, 'name', e.target.value)}
                              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                            />
                            <input
                              type="email"
                              placeholder="Email"
                              value={recipient.email}
                              onChange={(e) => handleRecipientChange(index, 'email', e.target.value)}
                              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                            />
                            {globalSettings.notificationRecipients.length > 1 && (
                              <button
                                onClick={() => handleRemoveRecipient(index)}
                                className="text-red-400 hover:text-red-600"
                                title="Remove recipient"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        ))}
                        <button
                          onClick={handleAddRecipient}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
                        >
                          <Plus className="h-3 w-3" />
                          <span>Add Recipient</span>
                        </button>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Language</h4>
                      <select
                        value={globalSettings.preferredLanguage}
                        onChange={(e) => handleGlobalSettingsChange('preferredLanguage', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="English">English</option>
                        <option value="Swedish">Swedish</option>
                        <option value="Spanish">Spanish</option>
                        <option value="French">French</option>
                        <option value="German">German</option>
                      </select>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={handleSaveGlobalSettings}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium"
                  >
                    Save Global Settings
                  </button>
                </div>
              </div>
            </Card>

            <Card title="Document Types & Storage" description="Configure specific document types, processing settings, and storage locations">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Specific Document Types</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Configure how specific document types are processed and stored</p>
                  </div>
                  <button
                    onClick={handleAddDocType}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium flex items-center space-x-2"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add Document Type</span>
                  </button>
                </div>

                {/* Add Document Type Modal */}
                {showAddDocType && (
                  <AddDocumentTypeModal
                    onClose={() => setShowAddDocType(false)}
                    onSave={async (docTypeData) => {
                      try {
                        const updatedDocTypes = {
                          ...configData.document_types,
                          [docTypeData.name]: {
                            keywords: docTypeData.keywords.split(',').map((k: string) => k.trim()),
                            actions: {
                              upload: docTypeData.upload,
                              notify: docTypeData.notify
                            },
                            ...(docTypeData.upload && docTypeData.storagePath && {
                              storage: {
                                subfolder_format: docTypeData.storagePath
                              }
                            }),
                            ...(docTypeData.notify && docTypeData.recipients.some((r: any) => r.email) && {
                              notification: {
                                recipients: docTypeData.recipients.filter((r: any) => r.email && r.name)
                              },
                              preferred_language: docTypeData.preferredLanguage
                            })
                          }
                        };

                        const updatedConfig = {
                          ...configData,
                          document_types: updatedDocTypes
                        };

                        await autoSaveConfig(updatedConfig);
                        setShowAddDocType(false);
                        toast.success(`Document type "${docTypeData.name}" added successfully`);
                      } catch (error) {
                        console.error('Error adding document type:', error);
                        toast.error('Failed to add document type');
                      }
                    }}
                    globalSettings={globalSettings}
                  />
                )}

                {/* Edit Document Type Modal */}
                {showEditDocType && editingDocType && (
                  <EditDocumentTypeModal
                    docTypeData={editingDocType}
                    onClose={() => {
                      setShowEditDocType(false);
                      setEditingDocType(null);
                    }}
                    onSave={handleUpdateDocType}
                    globalSettings={globalSettings}
                  />
                )}

                <div className="space-y-6">
                  {configData?.document_types && Object.keys(configData.document_types).length > 0 ? (
                    Object.entries(configData.document_types).map(([docType, config]: [string, any]) => (
                      <DocumentTypeCard
                        key={docType}
                        docType={docType}
                        config={config}
                        onEdit={() => handleEditDocType(docType, config)}
                        onDelete={() => handleDeleteDocType(docType)}
                        onUpdate={handleDocTypeChange}
                        globalSettings={globalSettings}
                      />
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No Document Types Configured</p>
                      <p className="text-sm">Add document types to start processing emails automatically.</p>
                    </div>
                  )}
                </div>

                {/* Auto-save Status */}
                <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    {autoSaveStatus === 'saving' && (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">Saving...</span>
                      </>
                    )}
                    {autoSaveStatus === 'saved' && lastAutoSave && (
                      <>
                        <div className="h-4 w-4 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                            <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                          </svg>
                        </div>
                        <span className="text-sm text-green-600 dark:text-green-400">
                          Auto-saved at {lastAutoSave.toLocaleTimeString()}
                        </span>
                      </>
                    )}
                    {autoSaveStatus === 'error' && (
                      <>
                        <div className="h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
                          <X className="h-2 w-2 text-white" />
                        </div>
                        <span className="text-sm text-red-600 dark:text-red-400">Save failed</span>
                      </>
                    )}
                    {autoSaveStatus === 'idle' && (
                      <span className="text-sm text-gray-500 dark:text-gray-400">All changes saved automatically</span>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'billing' && (
          <div className="space-y-6">
            <Card title="Current Plan" description="Manage your subscription">
              <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                    {user?.subscription?.plan === 'business' ? 'Business Plan' : 'Starter Plan'}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Next billing: {user?.subscription?.nextBilling}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${user?.subscription?.plan === 'business' ? '149' : '49'}/month
                  </p>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${user?.subscription?.status === 'active'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                    {user?.subscription?.status?.charAt(0).toUpperCase() + user?.subscription?.status?.slice(1)}
                  </span>
                </div>
              </div>

              <div className="flex space-x-3 mt-4">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
                  onClick={() => toast.success('Plan upgrade feature coming soon')}
                >
                  {user?.subscription?.plan === 'business' ? 'Manage Plan' : 'Upgrade Plan'}
                </button>
                <button
                  className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 px-4 py-2 rounded-md font-medium"
                  onClick={() => toast.success('Billing portal feature coming soon')}
                >
                  View Billing History
                </button>
              </div>
            </Card>

            <Card title="Usage This Month" description="Track your current usage">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Documents Processed</span>
                    <span className="text-sm font-medium">2,450 / {user?.subscription?.plan === 'business' ? '10,000' : '1,000'}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: user?.subscription?.plan === 'business' ? '24.5%' : '245%' }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Active Mailboxes</span>
                    <span className="text-sm font-medium">{mailboxes.filter(m => m.isActive).length} / {user?.subscription?.plan === 'business' ? 'Unlimited' : '3'}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: user?.subscription?.plan === 'business' ? '10%' : '66.7%' }}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </Layout>
  );
};

// Add Document Type Modal Component
interface AddDocumentTypeModalProps {
  onClose: () => void;
  onSave: (docTypeData: any) => void;
  globalSettings: any;
}

const AddDocumentTypeModal: React.FC<AddDocumentTypeModalProps> = ({ onClose, onSave, globalSettings }) => {
  const [formData, setFormData] = useState({
    name: '',
    keywords: '',
    upload: false,
    notify: false,
    storagePath: '{doc_type}/{document_year}/{company_name}',
    recipients: [{ name: '', email: '' }],
    preferredLanguage: globalSettings.preferredLanguage
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Document type name is required');
      return;
    }
    if (!formData.keywords.trim()) {
      toast.error('Keywords are required');
      return;
    }
    onSave(formData);
  };

  const handleRecipientChange = (index: number, field: 'name' | 'email', value: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) =>
        i === index ? { ...recipient, [field]: value } : recipient
      )
    }));
  };

  const addRecipient = () => {
    setFormData(prev => ({
      ...prev,
      recipients: [...prev.recipients, { name: '', email: '' }]
    }));
  };

  const removeRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Add Document Type</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Document Type Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Certificates, Invoices"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Keywords (comma-separated)
              </label>
              <input
                type="text"
                value={formData.keywords}
                onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="certificate, cert, certification"
                required
              />
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Processing Options</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.upload}
                  onChange={(e) => setFormData(prev => ({ ...prev, upload: e.target.checked }))}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Upload documents</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notify}
                  onChange={(e) => setFormData(prev => ({ ...prev, notify: e.target.checked }))}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Send notifications</span>
              </label>
            </div>
          </div>

          {formData.upload && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Storage Path
              </label>
              <input
                type="text"
                value={formData.storagePath}
                onChange={(e) => setFormData(prev => ({ ...prev, storagePath: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="{doc_type}/{document_year}/{company_name}"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Variables: {'{doc_type}'}, {'{document_year}'}, {'{company_name}'}
              </p>
            </div>
          )}

          {formData.notify && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Notification Recipients
                </label>
                <div className="space-y-2">
                  {formData.recipients.map((recipient, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        placeholder="Name"
                        value={recipient.name}
                        onChange={(e) => handleRecipientChange(index, 'name', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="email"
                        placeholder="Email"
                        value={recipient.email}
                        onChange={(e) => handleRecipientChange(index, 'email', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                      {formData.recipients.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeRecipient(index)}
                          className="text-red-400 hover:text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addRecipient}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
                  >
                    <Plus className="h-3 w-3" />
                    <span>Add Recipient</span>
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Preferred Language
                </label>
                <select
                  value={formData.preferredLanguage}
                  onChange={(e) => setFormData(prev => ({ ...prev, preferredLanguage: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="English">English</option>
                  <option value="Swedish">Swedish</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                </select>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
            >
              Add Document Type
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Edit Document Type Modal Component
interface EditDocumentTypeModalProps {
  docTypeData: any;
  onClose: () => void;
  onSave: (docTypeData: any) => void;
  globalSettings: any;
}

const EditDocumentTypeModal: React.FC<EditDocumentTypeModalProps> = ({
  docTypeData,
  onClose,
  onSave,
  globalSettings
}) => {
  const [formData, setFormData] = useState({
    name: docTypeData.name || '',
    originalName: docTypeData.originalName || docTypeData.name || '',
    keywords: docTypeData.keywords || '',
    upload: docTypeData.upload || false,
    notify: docTypeData.notify || false,
    storagePath: docTypeData.storagePath || `{doc_type}/{document_year}/{company_name}`,
    recipients: docTypeData.recipients && docTypeData.recipients.length > 0
      ? docTypeData.recipients
      : [{ name: '', email: '' }],
    preferredLanguage: docTypeData.preferredLanguage || globalSettings.preferredLanguage
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Document type name is required');
      return;
    }
    if (!formData.keywords.trim()) {
      toast.error('Keywords are required');
      return;
    }
    onSave(formData);
  };

  const handleRecipientChange = (index: number, field: 'name' | 'email', value: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) =>
        i === index ? { ...recipient, [field]: value } : recipient
      )
    }));
  };

  const addRecipient = () => {
    setFormData(prev => ({
      ...prev,
      recipients: [...prev.recipients, { name: '', email: '' }]
    }));
  };

  const removeRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Edit Document Type: {formData.originalName}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Document Type Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Certificates, Invoices"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Keywords (comma-separated)
              </label>
              <input
                type="text"
                value={formData.keywords}
                onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="certificate, cert, certification"
                required
              />
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Processing Options</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.upload}
                  onChange={(e) => setFormData(prev => ({ ...prev, upload: e.target.checked }))}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Upload documents</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notify}
                  onChange={(e) => setFormData(prev => ({ ...prev, notify: e.target.checked }))}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Send notifications</span>
              </label>
            </div>
          </div>

          {formData.upload && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Storage Path
              </label>
              <input
                type="text"
                value={formData.storagePath}
                onChange={(e) => setFormData(prev => ({ ...prev, storagePath: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                placeholder="{doc_type}/{document_year}/{company_name}"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Variables: {'{doc_type}'}, {'{document_year}'}, {'{company_name}'}
              </p>
            </div>
          )}

          {formData.notify && (
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Specific Notification Recipients
                  </label>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Leave empty to use global recipients
                  </div>
                </div>
                <div className="space-y-2">
                  {formData.recipients.map((recipient, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        placeholder="Name"
                        value={recipient.name}
                        onChange={(e) => handleRecipientChange(index, 'name', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="email"
                        placeholder="Email"
                        value={recipient.email}
                        onChange={(e) => handleRecipientChange(index, 'email', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      />
                      {formData.recipients.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeRecipient(index)}
                          className="text-red-400 hover:text-red-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addRecipient}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center space-x-1"
                  >
                    <Plus className="h-3 w-3" />
                    <span>Add Recipient</span>
                  </button>
                </div>
                <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    💡 <strong>Tip:</strong> If no specific recipients are configured, notifications will be sent to the global recipients configured in Global Settings.
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Preferred Language
                </label>
                <select
                  value={formData.preferredLanguage}
                  onChange={(e) => setFormData(prev => ({ ...prev, preferredLanguage: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="English">English</option>
                  <option value="Swedish">Swedish</option>
                  <option value="Spanish">Spanish</option>
                  <option value="French">French</option>
                  <option value="German">German</option>
                </select>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
            >
              Update Document Type
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Document Type Card Component
interface DocumentTypeCardProps {
  docType: string;
  config: any;
  onEdit: () => void;
  onDelete: () => void;
  onUpdate: (docType: string, field: string, value: any) => void;
  globalSettings: any;
}

const DocumentTypeCard: React.FC<DocumentTypeCardProps> = ({
  docType,
  config,
  onEdit,
  onDelete,
  onUpdate,
  globalSettings
}) => {
  const [localConfig, setLocalConfig] = useState(config);

  const handleCheckboxChange = async (field: string, checked: boolean) => {
    const newConfig = {
      ...localConfig,
      actions: {
        ...localConfig.actions,
        [field]: checked
      }
    };
    setLocalConfig(newConfig);
    await onUpdate(docType, 'actions', newConfig.actions);
  };

  const handleStoragePathChange = async (value: string) => {
    const newConfig = {
      ...localConfig,
      storage: {
        ...localConfig.storage,
        subfolder_format: value
      }
    };
    setLocalConfig(newConfig);
    await onUpdate(docType, 'storage', newConfig.storage);
  };

  return (
    <div className="p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
      {/* Document Type Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg">
            <FileText className="h-6 w-6" />
          </div>
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white capitalize">
              {docType.replace('_', ' ')}
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Keywords: {config.keywords ? config.keywords.join(', ') : 'None'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={onEdit}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
            title="Edit document type"
          >
            <SettingsIcon className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-400"
            title="Delete document type"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Processing Options */}
      <div className="space-y-4">
        <div>
          <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Processing Options</h5>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={localConfig.actions?.upload || false}
                onChange={(e) => handleCheckboxChange('upload', e.target.checked)}
                className="form-checkbox h-4 w-4 text-blue-600"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Upload documents</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={localConfig.actions?.notify || false}
                onChange={(e) => handleCheckboxChange('notify', e.target.checked)}
                className="form-checkbox h-4 w-4 text-blue-600"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Send notifications</span>
            </label>
          </div>
        </div>

        {/* Storage Configuration - Only show if Upload is enabled */}
        {localConfig.actions?.upload && (
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Storage Location</h5>
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Storage Path
                </label>
                <input
                  type="text"
                  value={localConfig.storage?.subfolder_format || `${docType}/{document_year}/{company_name}`}
                  onChange={(e) => handleStoragePathChange(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Variables: {'{doc_type}'}, {'{document_year}'}, {'{company_name}'}
                </p>
              </div>

              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Preview:</p>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-mono">
                  📁 {(localConfig.storage?.subfolder_format || `${docType}/{document_year}/{company_name}`)
                    .replace('{doc_type}', docType.charAt(0).toUpperCase() + docType.slice(1).replace('_', ' '))
                    .replace('{document_year}', '2024')
                    .replace('{company_name}', 'Acme Corp')}/
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Notification Configuration - Only show if Notify is enabled */}
        {localConfig.actions?.notify && (
          <div>
            <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Notification Settings</h5>
            <div className="space-y-3">
              {/* Specific Recipients for this Document Type */}
              {config.notification?.recipients && config.notification.recipients.length > 0 ? (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      Specific Recipients ({config.notification.recipients.length})
                    </p>
                    <button
                      onClick={onEdit}
                      className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                      title="Edit recipients"
                    >
                      Edit Recipients
                    </button>
                  </div>
                  <div className="space-y-1">
                    {config.notification.recipients.slice(0, 3).map((recipient: any, index: number) => (
                      <div key={index} className="flex items-center space-x-2 text-xs">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-gray-700 dark:text-gray-300">{recipient.name}</span>
                        <span className="text-gray-500 dark:text-gray-400">({recipient.email})</span>
                      </div>
                    ))}
                    {config.notification.recipients.length > 3 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 ml-4">
                        +{config.notification.recipients.length - 3} more recipients
                      </p>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                    Language: {config.preferred_language || globalSettings.preferredLanguage}
                  </p>
                </div>
              ) : (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-yellow-700 dark:text-yellow-300">
                        📧 Using Global Recipients ({globalSettings.notificationRecipients.filter((r: any) => r.email).length} recipients)
                      </p>
                      <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                        Language: {globalSettings.preferredLanguage}
                      </p>
                    </div>
                    <button
                      onClick={onEdit}
                      className="text-yellow-700 hover:text-yellow-900 dark:text-yellow-300 dark:hover:text-yellow-100 text-xs font-medium"
                      title="Add specific recipients"
                    >
                      Add Specific
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;
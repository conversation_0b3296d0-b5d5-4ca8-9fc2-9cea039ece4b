import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Settings, LogOut, Shield, Sun, Moon, ToggleLeft as Toggle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useApp } from '../../contexts/AppContext';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDevelopmentMode, toggleDevelopmentMode, theme, toggleTheme, environment } = useApp();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  if (!user) return null;

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Environment Badge */}
          <div className="flex items-center space-x-4">
            <Link to="/dashboard" className="flex items-center space-x-2">
              <Mail className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900 dark:text-white">Mail_Auto</span>
            </Link>

            {/* Environment Badge - Only show for admin users */}
            {user.role === 'admin' && (
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${environment === 'development'
                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                }`}>
                {environment.toUpperCase()}
              </span>
            )}
          </div>

          {/* Navigation and Controls */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {theme === 'light' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
            </button>

            {/* Development Mode Toggle (Admin Only) */}
            {user.role === 'admin' && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Dev Mode</span>
                <button
                  onClick={toggleDevelopmentMode}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${isDevelopmentMode ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${isDevelopmentMode ? 'translate-x-6' : 'translate-x-1'
                      }`}
                  />
                </button>
              </div>
            )}

            {/* Admin Panel Link */}
            {user.role === 'admin' && (
              <Link
                to="/admin"
                className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
              >
                <Shield className="h-4 w-4" />
                <span>Admin</span>
              </Link>
            )}

            {/* Settings Link - Only for customers, not admin */}
            {user.role !== 'admin' && (
              <Link
                to="/settings"
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <Settings className="h-5 w-5" />
              </Link>
            )}

            {/* User Menu */}
            <div className="flex items-center space-x-3">
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-white">{user.name}</div>
                <div className="text-gray-500 dark:text-gray-400">{user.email}</div>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
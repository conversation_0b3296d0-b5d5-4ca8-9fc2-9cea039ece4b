"""End-to-end orchestrator for the multi-tenant email-to-OneDrive pipeline.

Usage (interactive):
    python main.py [--once]

If --once is supplied the script processes unread e-mails once and exits.
Otherwise it runs forever with a sleep between cycles (default 5 min).
"""
from __future__ import annotations

import argparse
import time
from datetime import datetime

from core.tenant_loader import list_tenants
from core.mail_reader import read_mail, read_mail_multi_mailbox, get_tenant_mail_headers
from core.unified_file_analyzer import analyze_file_bytes
from core.mailbox_manager import MailboxConfigManager
from core.tracking.config import setup_tracking_for_tenant

CYCLE_SECONDS = 60*60  # Waiting 1 hour between cycles seconds

def process_tenants() -> None:
    tenants = list_tenants()
    if not tenants:
        print("⚠️  No tenants configured – nothing to do.")
        return

    for tenant in tenants:
        tenant_name, _creds_path, tenant_cfg, _token_cache = tenant
        print("\n==============================")
        print(f"🏢 Tenant: {tenant_name}  |  {datetime.now().isoformat(timespec='seconds')}")
        print("==============================")

        # Initialize tracking for this tenant
        try:
            tracking_config = setup_tracking_for_tenant(tenant_cfg)
            print(f"📊 Tracking initialized for {tenant_name} (enabled: {tracking_config.enabled})")
        except Exception as e:
            print(f"⚠️ Failed to initialize tracking for {tenant_name}: {e}")

        # Check if this tenant has multi-mailbox configuration
        mailbox_manager = MailboxConfigManager(tenant_cfg)

        if mailbox_manager.has_mailbox_configuration():
            # Use multi-mailbox reader
            print("📬 Multi-mailbox configuration detected")
            attachments = read_mail_multi_mailbox(tenant)

            if not attachments:
                continue

            headers = get_tenant_mail_headers(tenant)
            if not headers:
                print("❌ Could not obtain auth headers – skipping tenant.")
                continue

            # Process attachments with mailbox context
            for file_bytes, filename, sender, received, mail_body, mime_type, mailbox_email in attachments:
                print(f"\n🔄 Processing attachment {filename} from {sender} ({received}) via {mailbox_email}")
                analyze_file_bytes_with_mailbox(
                    file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email
                )
        else:
            # Legacy single-mailbox behavior
            print("📧 Single-mailbox configuration detected")
            attachments = read_mail(tenant)
            if not attachments:
                continue

            headers = get_tenant_mail_headers(tenant)
            if not headers:
                print("❌ Could not obtain auth headers – skipping tenant.")
                continue

            for file_bytes, filename, sender, received, mail_body, mime_type in attachments:
                print(f"\n🔄 Processing attachment {filename} from {sender} ({received})")
                analyze_file_bytes(file_bytes, tenant_cfg, filename, headers, mail_body, mime_type)


def analyze_file_bytes_with_mailbox(
    file_bytes: bytes,
    tenant_cfg: dict,
    filename: str,
    headers: dict,
    mail_body: str,
    mime_type: str,
    mailbox_email: str
) -> None:
    """
    Analyze file bytes with mailbox context for multi-mailbox setups.
    This wrapper ensures that notifications are sent from the correct mailbox.
    """
    # Import here to avoid circular imports
    from core.unified_file_analyzer import analyze_file_bytes_internal

    # Call the internal analyzer with mailbox context
    analyze_file_bytes_internal(
        file_bytes, tenant_cfg, filename, headers, mail_body, mime_type, mailbox_email
    )


def main() -> None:

    parser = argparse.ArgumentParser(description="Run the email-to-OneDrive pipeline")
    parser.add_argument("--once", action="store_true", help="Run a single cycle and exit")
    parser.add_argument(
        "--interval",
        type=int,
        default=CYCLE_SECONDS,
        help="Seconds to wait between cycles (default 300)",
    )
    args = parser.parse_args()

    if args.once:
        process_tenants()
    else:
        while True:
            process_tenants()
            print(f"\n⏳ Sleeping {args.interval}s …")
            time.sleep(args.interval)


if __name__ == "__main__":
    main()

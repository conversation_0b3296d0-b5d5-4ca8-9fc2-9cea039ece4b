import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, CheckCircle, ArrowRight, ArrowLeft, FileText, Settings, Zap } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const Onboarding: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();

  const steps = [
    {
      title: 'Welcome to Mail_Auto',
      description: 'Let\'s get you set up with automated document processing',
      icon: <Mail className="h-12 w-12 text-blue-600" />
    },
    {
      title: 'Configure Your First Mailbox',
      description: 'Set up an email address for document processing',
      icon: <Settings className="h-12 w-12 text-blue-600" />
    },
    {
      title: 'Choose Document Types',
      description: 'Select which types of documents you want to process',
      icon: <FileText className="h-12 w-12 text-blue-600" />
    },
    {
      title: 'All Set!',
      description: 'You\'re ready to start processing documents',
      icon: <Zap className="h-12 w-12 text-blue-600" />
    }
  ];

  const [formData, setFormData] = useState({
    mailboxEmail: '',
    mailboxName: '',
    documentTypes: [] as string[]
  });

  const handleNext = () => {
    if (currentStep === 1) {
      if (!formData.mailboxEmail || !formData.mailboxName) {
        toast.error('Please fill in all mailbox fields');
        return;
      }
    }
    
    if (currentStep === 2) {
      if (formData.documentTypes.length === 0) {
        toast.error('Please select at least one document type');
        return;
      }
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      toast.success('Setup completed successfully!');
      navigate('/dashboard');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDocumentTypeChange = (type: string) => {
    setFormData(prev => ({
      ...prev,
      documentTypes: prev.documentTypes.includes(type)
        ? prev.documentTypes.filter(t => t !== type)
        : [...prev.documentTypes, type]
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Mail className="h-12 w-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          Welcome, {user?.name}!
        </h2>
        
        {/* Progress Steps */}
        <div className="mt-8 flex justify-center">
          <div className="flex items-center space-x-4">
            {steps.map((_, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {index < currentStep ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 ml-4 ${
                    index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              {steps[currentStep].icon}
            </div>
            <h3 className="text-xl font-semibold text-gray-900">
              {steps[currentStep].title}
            </h3>
            <p className="mt-2 text-sm text-gray-600">
              {steps[currentStep].description}
            </p>
          </div>

          {/* Step Content */}
          <div className="space-y-6">
            {currentStep === 0 && (
              <div className="text-center space-y-4">
                <p className="text-gray-600">
                  We'll help you set up your first mailbox and configure document processing in just a few simple steps.
                </p>
                <div className="grid grid-cols-1 gap-4 text-left">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Automated Processing</p>
                      <p className="text-sm text-gray-600">Documents are processed automatically when received</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Secure & Reliable</p>
                      <p className="text-sm text-gray-600">Bank-level security with 99.9% uptime</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Easy Integration</p>
                      <p className="text-sm text-gray-600">Works with your existing email infrastructure</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 1 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.mailboxEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, mailboxEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This is where you'll forward documents for processing
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name
                  </label>
                  <input
                    type="text"
                    value={formData.mailboxName}
                    onChange={(e) => setFormData(prev => ({ ...prev, mailboxName: e.target.value }))}
                    placeholder="Document Processing"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-4">
                  Select the types of documents you want to process automatically:
                </p>
                <div className="space-y-3">
                  {[
                    { value: 'invoice', label: 'Invoices', description: 'Bills, receipts, and payment documents' },
                    { value: 'certificate', label: 'Certificates', description: 'Quality certificates, compliance documents' },
                    { value: 'contract', label: 'Contracts', description: 'Agreements, terms, and legal documents' },
                    { value: 'custom', label: 'Custom Documents', description: 'Other document types you define' }
                  ].map((type) => (
                    <div key={type.value} className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        id={type.value}
                        checked={formData.documentTypes.includes(type.value)}
                        onChange={() => handleDocumentTypeChange(type.value)}
                        className="form-checkbox h-4 w-4 text-blue-600 mt-1"
                      />
                      <div>
                        <label htmlFor={type.value} className="font-medium text-gray-900">
                          {type.label}
                        </label>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="text-center space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="font-medium text-green-800">Setup Complete!</p>
                  <p className="text-sm text-green-600 mt-1">
                    Your account is ready for document processing
                  </p>
                </div>
                <div className="text-left space-y-2">
                  <h4 className="font-medium text-gray-900">What's next?</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Start forwarding documents to {formData.mailboxEmail}</li>
                    <li>• Monitor processing in your dashboard</li>
                    <li>• Configure additional mailboxes as needed</li>
                    <li>• Set up API integrations (optional)</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="mt-8 flex justify-between">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md ${
                currentStep === 0
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:text-gray-900'
              }`}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Previous</span>
            </button>

            <button
              onClick={handleNext}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
            >
              <span>{currentStep === steps.length - 1 ? 'Get Started' : 'Next'}</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
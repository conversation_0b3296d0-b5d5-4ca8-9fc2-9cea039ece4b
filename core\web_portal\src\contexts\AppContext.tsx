import React, { createContext, useContext, useState, useEffect } from 'react';

interface AppContextType {
  isDevelopmentMode: boolean;
  toggleDevelopmentMode: () => void;
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  environment: 'development' | 'production';
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize theme from localStorage or default to light
  const [theme, setTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('mail-auto-theme');
      return (savedTheme as 'light' | 'dark') || 'light';
    }
    return 'light';
  });

  // Initialize development mode from localStorage or default to true
  const [isDevelopmentMode, setIsDevelopmentMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('mail-auto-dev-mode');
      return savedMode ? JSON.parse(savedMode) : true;
    }
    return true;
  });

  // Apply theme to document and save to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement;
      if (theme === 'dark') {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
      localStorage.setItem('mail-auto-theme', theme);
    }
  }, [theme]);

  // Save development mode to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('mail-auto-dev-mode', JSON.stringify(isDevelopmentMode));
    }
  }, [isDevelopmentMode]);

  const toggleDevelopmentMode = () => {
    setIsDevelopmentMode(!isDevelopmentMode);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const environment = isDevelopmentMode ? 'development' : 'production';

  return (
    <AppContext.Provider value={{
      isDevelopmentMode,
      toggleDevelopmentMode,
      theme,
      toggleTheme,
      environment
    }}>
      {children}
    </AppContext.Provider>
  );
};
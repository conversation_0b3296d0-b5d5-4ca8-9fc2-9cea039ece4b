import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Mail, Zap, Shield, BarChart3, CheckCircle, ArrowRight, Star, 
  Users, Building2, TrendingUp, Clock, FileText, Bell, 
  ChevronRight, Play, Quote, Award, Globe, Sparkles,
  Bot, Filter, Archive, Send
} from 'lucide-react';

const LandingPage: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const workflowSteps = [
    {
      icon: <Mail className="h-8 w-8" />,
      title: "Email Received",
      description: "Documents arrive in your dedicated mailbox",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Bot className="h-8 w-8" />,
      title: "AI Analysis",
      description: "Advanced AI extracts key data and classifies documents",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <Filter className="h-8 w-8" />,
      title: "Smart Organization",
      description: "Automatically sorted and structured for your workflow",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: <Bell className="h-8 w-8" />,
      title: "Instant Notifications",
      description: "Real-time alerts and seamless integrations",
      color: "from-orange-500 to-red-500"
    }
  ];

  const features = [
    {
      icon: <Zap className="h-8 w-8 text-blue-600" />,
      title: 'AI-Powered Processing',
      description: 'Advanced machine learning extracts data with 99.7% accuracy, eliminating manual data entry forever.',
      stats: '99.7% accuracy'
    },
    {
      icon: <Shield className="h-8 w-8 text-blue-600" />,
      title: 'Enterprise Security',
      description: 'SOC 2 compliant with end-to-end encryption. Your data is protected with bank-level security.',
      stats: 'SOC 2 certified'
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-blue-600" />,
      title: 'Advanced Analytics',
      description: 'Real-time insights and reporting help you optimize processes and track ROI across your organization.',
      stats: '10x faster insights'
    },
    {
      icon: <Globe className="h-8 w-8 text-blue-600" />,
      title: 'Global Scale',
      description: 'Process documents in 40+ languages with 99.9% uptime. Built for enterprise scale.',
      stats: '40+ languages'
    },
    {
      icon: <Sparkles className="h-8 w-8 text-blue-600" />,
      title: 'Smart Integrations',
      description: 'Connect with 500+ apps including Salesforce, QuickBooks, and your existing workflow tools.',
      stats: '500+ integrations'
    },
    {
      icon: <Award className="h-8 w-8 text-blue-600" />,
      title: 'Compliance Ready',
      description: 'GDPR, HIPAA, and industry-specific compliance built-in. Audit trails for every document.',
      stats: 'Full compliance'
    }
  ];

  const pricingPlans = [
    {
      name: 'Free',
      price: '$0',
      description: 'Perfect for testing and small projects',
      features: [
        '1 email mailbox',
        'Up to 5 documents/month',
        'Basic document types',
        'Email support',
        'Standard processing speed'
      ],
      cta: 'Start Free',
      popular: false,
      gradient: 'from-gray-50 to-gray-100'
    },
    {
      name: 'Starter',
      price: '$49',
      description: 'Ideal for small businesses and teams',
      features: [
        'Up to 3 email mailboxes',
        'Up to 1,000 documents/month',
        'All document types',
        'Priority email support',
        'API access',
        'Basic analytics',
        'Webhook integrations'
      ],
      cta: 'Start 14-day trial',
      popular: false,
      gradient: 'from-blue-50 to-indigo-100'
    },
    {
      name: 'Business',
      price: '$149',
      description: 'Perfect for growing companies',
      features: [
        'Unlimited mailboxes',
        'Up to 10,000 documents/month',
        'Custom document types',
        '24/7 priority support',
        'Advanced analytics & reporting',
        'Custom workflows',
        'SSO integration',
        'Dedicated account manager'
      ],
      popular: true,
      cta: 'Start 14-day trial',
      gradient: 'from-blue-500 to-purple-600'
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      description: 'For large organizations with custom needs',
      features: [
        'Unlimited everything',
        'Custom integrations',
        'On-premise deployment',
        'Dedicated infrastructure',
        'Custom SLA',
        'Training & onboarding',
        'Advanced security features',
        'White-label options'
      ],
      cta: 'Contact Sales',
      popular: false,
      gradient: 'from-purple-50 to-pink-100'
    }
  ];

  const testimonials = [
    {
      quote: "Mail_Auto transformed our invoice processing from 3 hours to 3 minutes. The ROI was immediate and the accuracy is incredible.",
      author: "Sarah Chen",
      role: "CFO",
      company: "TechFlow Inc",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      stats: "95% time saved"
    },
    {
      quote: "We process 10,000+ documents monthly with zero errors. Mail_Auto's AI is remarkably accurate and the integrations are seamless.",
      author: "Marcus Rodriguez",
      role: "Operations Director",
      company: "Global Logistics Co",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      stats: "10,000+ docs/month"
    },
    {
      quote: "The compliance features and audit trails gave us confidence to automate our entire document workflow. Game-changing for our industry.",
      author: "Dr. Emily Watson",
      role: "Head of Compliance",
      company: "MedTech Solutions",
      avatar: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      stats: "100% compliant"
    }
  ];

  const stats = [
    { value: '50M+', label: 'Documents Processed' },
    { value: '99.7%', label: 'Accuracy Rate' },
    { value: '2,500+', label: 'Happy Customers' },
    { value: '40+', label: 'Countries Served' }
  ];

  const companies = [
    { name: 'TechFlow', logo: '🚀' },
    { name: 'GlobalCorp', logo: '🌐' },
    { name: 'InnovateLab', logo: '⚡' },
    { name: 'DataSync', logo: '📊' },
    { name: 'CloudFirst', logo: '☁️' },
    { name: 'NextGen', logo: '🔮' }
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Mail className="h-8 w-8 text-blue-600" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Mail_Auto
              </span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors">Features</a>
              <a href="#pricing" className="text-gray-700 hover:text-blue-600 transition-colors">Pricing</a>
              <a href="#testimonials" className="text-gray-700 hover:text-blue-600 transition-colors">Customers</a>
              <Link
                to="/login"
                className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/login"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Start Free
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative pt-20 pb-32 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full h-full">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Announcement Badge */}
            <div className={`inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <Sparkles className="h-4 w-4 mr-2" />
              New: Free plan now available - Start processing documents today
              <ChevronRight className="h-4 w-4 ml-2" />
            </div>

            <h1 className={`text-5xl md:text-7xl font-bold mb-8 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
                Automate Your Email
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Document Processing
              </span>
            </h1>

            <p className={`text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              Transform your inbox into an intelligent document processing engine. 
              <span className="font-semibold text-gray-900"> Save 95% of manual work</span> with AI that extracts, 
              classifies, and organizes documents automatically.
            </p>

            <div className={`flex flex-col sm:flex-row gap-4 justify-center mb-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              <Link
                to="/login"
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full text-lg font-semibold inline-flex items-center transition-all duration-200 transform hover:scale-105 shadow-xl hover:shadow-2xl"
              >
                Start Free - No Credit Card
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button className="group border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 rounded-full text-lg font-semibold inline-flex items-center transition-all duration-200 hover:bg-blue-50">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-gray-600 font-medium mb-8">
            Trusted by innovative companies worldwide
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16 opacity-60">
            {companies.map((company, index) => (
              <div key={index} className="flex items-center space-x-3 text-gray-500 hover:text-gray-700 transition-colors">
                <span className="text-2xl">{company.logo}</span>
                <span className="font-semibold text-lg">{company.name}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Workflow Visualization */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From email to insights in seconds. Our AI-powered workflow transforms 
              how you handle documents forever.
            </p>
          </div>

          <div className="relative">
            {/* Workflow Steps */}
            <div className="grid md:grid-cols-4 gap-8 relative">
              {workflowSteps.map((step, index) => (
                <div key={index} className="relative">
                  {/* Connection Line */}
                  {index < workflowSteps.length - 1 && (
                    <div className="hidden md:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-gray-300 to-gray-200 z-0">
                      <div className="absolute top-1/2 right-4 transform -translate-y-1/2">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  )}
                  
                  {/* Step Card */}
                  <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-blue-200 group">
                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${step.color} flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      {step.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{step.description}</p>
                    
                    {/* Step Number */}
                    <div className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {index + 1}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Demo Animation */}
            <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8 md:p-12">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">See It In Action</h3>
                <p className="text-gray-600">Watch how a typical invoice gets processed in real-time</p>
              </div>
              
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="text-sm text-gray-500">Processing: invoice_2024_001.pdf</div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <span className="text-sm">Email <NAME_EMAIL></span>
                    <div className="ml-auto w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <Bot className="h-5 w-5 text-purple-600" />
                    <span className="text-sm">AI extracting: Invoice #INV-2024-001, Amount: $1,250.00</span>
                    <div className="ml-auto w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                    <Archive className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Categorized as "Office Supplies" → QuickBooks</span>
                    <div className="ml-auto w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
                    <Send className="h-5 w-5 text-orange-600" />
                    <span className="text-sm">Notification <NAME_EMAIL></span>
                    <CheckCircle className="ml-auto h-5 w-5 text-green-600" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Enterprise-Grade Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Built for scale, security, and seamless integration with your existing workflow
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-blue-200">
                <div className="flex items-center justify-between mb-6">
                  <div className="p-3 bg-blue-50 rounded-xl group-hover:bg-blue-100 transition-colors">
                    {feature.icon}
                  </div>
                  <span className="text-sm font-semibold text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                    {feature.stats}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Loved by Teams Worldwide
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how companies are transforming their document workflows with Mail_Auto
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="flex items-center mb-6">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <Quote className="h-8 w-8 text-blue-600 mb-4" />
                <blockquote className="text-gray-700 text-lg leading-relaxed mb-6">
                  "{testimonial.quote}"
                </blockquote>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img 
                      src={testimonial.avatar} 
                      alt={testimonial.author}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.author}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                      <div className="text-sm text-blue-600 font-medium">{testimonial.company}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600">{testimonial.stats}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Start free, scale as you grow. No hidden fees, cancel anytime.
            </p>
            <div className="inline-flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
              <Sparkles className="h-4 w-4 mr-2" />
              14-day free trial on all paid plans
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`relative rounded-3xl p-8 transition-all duration-300 hover:scale-105 ${
                  plan.popular 
                    ? 'bg-gradient-to-br from-blue-600 to-purple-600 text-white shadow-2xl ring-4 ring-blue-200' 
                    : 'bg-white shadow-lg hover:shadow-xl border border-gray-200'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-4 py-2 text-sm font-bold rounded-full shadow-lg">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className={`text-2xl font-bold mb-2 ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                    {plan.name}
                  </h3>
                  <div className={`text-5xl font-bold mb-2 ${plan.popular ? 'text-white' : 'text-gray-900'}`}>
                    {plan.price}
                    {plan.price !== 'Custom' && plan.price !== '$0' && (
                      <span className={`text-lg font-normal ${plan.popular ? 'text-blue-100' : 'text-gray-600'}`}>
                        /month
                      </span>
                    )}
                  </div>
                  <p className={`${plan.popular ? 'text-blue-100' : 'text-gray-600'}`}>
                    {plan.description}
                  </p>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <CheckCircle className={`h-5 w-5 mr-3 ${plan.popular ? 'text-green-300' : 'text-green-500'}`} />
                      <span className={`${plan.popular ? 'text-blue-100' : 'text-gray-700'}`}>
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                <Link
                  to="/login"
                  className={`w-full text-center py-4 px-6 rounded-2xl font-semibold transition-all duration-200 transform hover:scale-105 ${
                    plan.popular
                      ? 'bg-white text-blue-600 hover:bg-gray-50 shadow-lg'
                      : plan.name === 'Free'
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg'
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg'
                  }`}
                >
                  {plan.cta}
                </Link>

                {plan.name === 'Free' && (
                  <p className="text-center text-sm text-gray-500 mt-3">
                    No credit card required
                  </p>
                )}
              </div>
            ))}
          </div>

          {/* ROI Calculator */}
          <div className="mt-16 bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-200">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">Calculate Your ROI</h3>
              <p className="text-gray-600">See how much time and money you'll save with Mail_Auto</p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl">
                <div className="text-4xl font-bold text-blue-600 mb-2">95%</div>
                <div className="text-gray-700 font-medium">Time Saved</div>
                <div className="text-sm text-gray-600 mt-2">Average across all customers</div>
              </div>
              <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl">
                <div className="text-4xl font-bold text-green-600 mb-2">$50K+</div>
                <div className="text-gray-700 font-medium">Annual Savings</div>
                <div className="text-sm text-gray-600 mt-2">For mid-size companies</div>
              </div>
              <div className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl">
                <div className="text-4xl font-bold text-purple-600 mb-2">3 Weeks</div>
                <div className="text-gray-700 font-medium">Payback Period</div>
                <div className="text-sm text-gray-600 mt-2">Typical implementation</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Workflow?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of companies already saving time and money with automated document processing.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/login"
              className="bg-white text-blue-600 hover:bg-gray-50 px-8 py-4 rounded-full text-lg font-semibold inline-flex items-center transition-all duration-200 transform hover:scale-105 shadow-xl"
            >
              Start Free Today
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <button className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-200">
              Schedule Demo
            </button>
          </div>
          <p className="text-blue-100 text-sm mt-4">
            Free plan available • No credit card required • 14-day trial on paid plans
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Mail className="h-8 w-8 text-blue-400" />
                <span className="text-xl font-bold">Mail_Auto</span>
              </div>
              <p className="text-gray-400 mb-4">
                Automate your document processing with AI-powered email integration
              </p>
              <div className="flex space-x-4">
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">𝕏</span>
                </div>
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">in</span>
                </div>
                <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 cursor-pointer">
                  <span className="text-sm">f</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#features" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Security</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Mail_Auto. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-gray-400 mt-4 md:mt-0">
              <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-white transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>

      <style jsx>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
};

export default LandingPage;
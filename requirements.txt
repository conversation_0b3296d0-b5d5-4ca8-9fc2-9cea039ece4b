# Core Microsoft Graph and Authentication
msal>=1.24.0                      # Microsoft Authentication Library
requests>=2.31.0                  # HTTP requests library

# Document Processing - Core
pdfminer.six>=20221105            # PDF text extraction
pdf2image>=3.1.0                 # PDF to image conversion
pytesseract>=0.3.10              # OCR text extraction from images

# Document Processing - Multi-format support
python-docx>=0.8.11              # Microsoft Word (.docx) files
openpyxl>=3.1.0                  # Excel (.xlsx/.xls) files
python-pptx>=0.6.21              # PowerPoint (.pptx) files
Pillow>=10.0.0                   # Image processing (PIL) - enhanced version

# Text Processing and Encoding
charset-normalizer>=3.0.0        # Better text encoding detection

# AI and Machine Learning
openai>=1.0.0                    # OpenAI GPT API for document classification and analysis

# Web Framework and API Server
flask>=2.3.0                     # Web framework for OAuth callbacks and API server
flask-cors>=4.0.0                # Cross-Origin Resource Sharing for React frontend
werkzeug>=2.3.0                  # WSGI utility library (Flask dependency)

# Azure Cloud Integration
azure-keyvault-secrets>=4.7.0    # Azure Key Vault secrets client
azure-identity>=1.14.0           # Azure authentication library

# Environment and Configuration Management
python-dotenv>=1.0.0             # Environment variable management

# Database and Data Processing
# Note: sqlite3 is included in Python standard library

# Development and Testing (optional but recommended)
pytest>=7.4.0                    # Testing framework
pytest-mock>=3.11.0              # Mock utilities for testing
coverage>=7.3.0                  # Code coverage analysis

# Performance and Monitoring
psutil>=5.9.0                    # System and process utilities for monitoring

# Logging and Error Handling (enhanced)
structlog>=23.1.0                # Structured logging for better debugging

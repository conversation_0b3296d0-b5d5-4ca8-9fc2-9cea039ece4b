/**
 * Development Environment Indicator
 * Shows current environment and provides development tools
 */

import React, { useState, useEffect } from 'react';
import { Settings, Activity, Database, Wifi, WifiOff } from 'lucide-react';
import { healthApi, devApi } from '../services/api';

interface DevEnvironmentIndicatorProps {
  className?: string;
}

export const DevEnvironmentIndicator: React.FC<DevEnvironmentIndicatorProps> = ({ 
  className = '' 
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [healthData, setHealthData] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);
  
  const isDevelopment = import.meta.env.DEV;
  
  useEffect(() => {
    const checkConnection = async () => {
      const connected = await devApi.testConnection();
      setIsConnected(connected);
      
      if (connected) {
        try {
          const health = await healthApi.check();
          setHealthData(health);
        } catch (error) {
          console.error('Failed to get health data:', error);
        }
      }
    };
    
    checkConnection();
    
    // Check connection every 30 seconds in development
    if (isDevelopment) {
      const interval = setInterval(checkConnection, 30000);
      return () => clearInterval(interval);
    }
  }, [isDevelopment]);
  
  if (!isDevelopment) {
    return null; // Don't show in production
  }
  
  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className="bg-gray-900 text-white rounded-lg shadow-lg border border-gray-700">
        {/* Main indicator */}
        <div 
          className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-800 rounded-lg"
          onClick={() => setShowDetails(!showDetails)}
        >
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
            <span className="text-xs font-medium">DEV</span>
            {isConnected ? <Wifi size={14} /> : <WifiOff size={14} />}
          </div>
          <Settings size={14} className="text-gray-400" />
        </div>
        
        {/* Expanded details */}
        {showDetails && (
          <div className="border-t border-gray-700 p-3 space-y-2">
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-400">Backend:</span>
                <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              {healthData && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Processing:</span>
                    <span className={healthData.processing_active ? 'text-green-400' : 'text-yellow-400'}>
                      {healthData.processing_active ? 'Active' : 'Stopped'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-400">Tenants:</span>
                    <span className="text-blue-400">{healthData.tenants_count}</span>
                  </div>
                </>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-400">Frontend:</span>
                <span className="text-green-400">Vite:5173</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-400">API:</span>
                <span className="text-green-400">Flask:5000</span>
              </div>
            </div>
            
            {/* Quick actions */}
            <div className="border-t border-gray-700 pt-2 space-y-1">
              <button
                onClick={() => window.open('http://localhost:5000/api/health', '_blank')}
                className="w-full text-left text-xs text-blue-400 hover:text-blue-300 flex items-center gap-1"
              >
                <Activity size={12} />
                View API Health
              </button>
              
              <button
                onClick={() => {
                  console.log('Available API endpoints:');
                  devApi.getEndpoints().forEach(endpoint => console.log(`  ${endpoint}`));
                }}
                className="w-full text-left text-xs text-blue-400 hover:text-blue-300 flex items-center gap-1"
              >
                <Database size={12} />
                Log API Endpoints
              </button>
            </div>
            
            {/* Environment info */}
            <div className="border-t border-gray-700 pt-2 text-xs text-gray-500">
              <div>Mode: {import.meta.env.MODE}</div>
              <div>Hot Reload: Active</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DevEnvironmentIndicator;

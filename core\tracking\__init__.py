"""Document processing tracking and analytics module."""

from .models import (
    ProcessingEvent,
    ProcessingStatistics,
    ProcessingStatus,
    OperationType,
    TrackingDatabase
)

from .service import (
    DocumentTrackingService,
    get_tracking_service,
    initialize_tracking
)

from .logger import (
    DocumentProcessingLogger,
    LogLevel,
    get_logger
)

from .analytics import (
    DocumentAnalytics,
    export_analytics_to_json
)

from .config import (
    TrackingConfig,
    setup_tracking_for_tenant,
    get_default_tracking_config,
    validate_tracking_config,
    migrate_tenant_to_tracking,
    create_tracking_config_template
)

__all__ = [
    # Models
    "ProcessingEvent",
    "ProcessingStatistics", 
    "ProcessingStatus",
    "OperationType",
    "TrackingDatabase",
    
    # Service
    "DocumentTrackingService",
    "get_tracking_service",
    "initialize_tracking",
    
    # Logger
    "DocumentProcessingLogger",
    "LogLevel",
    "get_logger",

    # Analytics
    "DocumentAnalytics",
    "export_analytics_to_json",

    # Configuration
    "TrackingConfig",
    "setup_tracking_for_tenant",
    "get_default_tracking_config",
    "validate_tracking_config",
    "migrate_tenant_to_tracking",
    "create_tracking_config_template"
]

"""Configuration management for document tracking system."""

import os
from typing import Dict, Any, Optional, List
from .service import initialize_tracking


class TrackingConfig:
    """Configuration manager for tracking system."""
    
    def __init__(self, tenant_config: Dict[str, Any]):
        """
        Initialize tracking configuration from tenant config.
        
        Args:
            tenant_config: Tenant configuration dictionary
        """
        self.tenant_config = tenant_config
        self.tracking_config = tenant_config.get("defaults", {}).get("tracking", {})
    
    @property
    def enabled(self) -> bool:
        """Check if tracking is enabled for this tenant."""
        return self.tracking_config.get("enabled", True)
    
    @property
    def database_path(self) -> Optional[str]:
        """Get the database path for this tenant."""
        custom_path = self.tracking_config.get("database_path")
        if custom_path:
            return custom_path
        
        # Default path based on tenant name
        tenant_name = self.tenant_config.get("tenant_name", "unknown")
        tracking_dir = os.path.join("tracking", tenant_name)
        os.makedirs(tracking_dir, exist_ok=True)
        return os.path.join(tracking_dir, "document_tracking.db")
    
    @property
    def retention_days(self) -> int:
        """Get data retention period in days."""
        return self.tracking_config.get("retention_days", 365)
    
    @property
    def analytics_enabled(self) -> bool:
        """Check if analytics are enabled."""
        analytics_config = self.tracking_config.get("analytics", {})
        return analytics_config.get("enabled", True)
    
    @property
    def dashboard_days_back(self) -> int:
        """Get default number of days for dashboard analytics."""
        analytics_config = self.tracking_config.get("analytics", {})
        return analytics_config.get("dashboard_days_back", 30)
    
    @property
    def export_format(self) -> str:
        """Get preferred export format for analytics."""
        analytics_config = self.tracking_config.get("analytics", {})
        return analytics_config.get("export_format", "json")
    
    def initialize_tracking_service(self):
        """Initialize the global tracking service with this tenant's configuration."""
        initialize_tracking(
            db_path=self.database_path,
            enabled=self.enabled
        )
    
    def get_analytics_config(self) -> Dict[str, Any]:
        """Get analytics configuration as a dictionary."""
        return {
            "enabled": self.analytics_enabled,
            "dashboard_days_back": self.dashboard_days_back,
            "export_format": self.export_format,
            "retention_days": self.retention_days
        }


def setup_tracking_for_tenant(tenant_config: Dict[str, Any]) -> TrackingConfig:
    """
    Set up tracking for a specific tenant based on their configuration.
    
    Args:
        tenant_config: Tenant configuration dictionary
        
    Returns:
        TrackingConfig instance for the tenant
    """
    tracking_config = TrackingConfig(tenant_config)
    tracking_config.initialize_tracking_service()
    return tracking_config


def get_default_tracking_config() -> Dict[str, Any]:
    """
    Get the default tracking configuration that can be added to tenant configs.
    
    Returns:
        Dictionary with default tracking configuration
    """
    return {
        "tracking": {
            "enabled": True,
            "database_path": None,  # Will use default path based on tenant name
            "retention_days": 365,
            "analytics": {
                "enabled": True,
                "dashboard_days_back": 30,
                "export_format": "json"
            }
        }
    }


def validate_tracking_config(tenant_config: Dict[str, Any]) -> List[str]:
    """
    Validate tracking configuration and return any issues found.
    
    Args:
        tenant_config: Tenant configuration dictionary
        
    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []
    
    tracking_config = tenant_config.get("defaults", {}).get("tracking", {})
    
    # Check if tracking section exists
    if not tracking_config:
        return []  # No tracking config is valid (will use defaults)
    
    # Validate enabled field
    enabled = tracking_config.get("enabled")
    if enabled is not None and not isinstance(enabled, bool):
        errors.append("tracking.enabled must be a boolean value")
    
    # Validate database_path
    db_path = tracking_config.get("database_path")
    if db_path is not None and not isinstance(db_path, str):
        errors.append("tracking.database_path must be a string or null")
    
    # Validate retention_days
    retention = tracking_config.get("retention_days")
    if retention is not None:
        if not isinstance(retention, int) or retention < 1:
            errors.append("tracking.retention_days must be a positive integer")
    
    # Validate analytics config
    analytics = tracking_config.get("analytics", {})
    if analytics:
        analytics_enabled = analytics.get("enabled")
        if analytics_enabled is not None and not isinstance(analytics_enabled, bool):
            errors.append("tracking.analytics.enabled must be a boolean value")
        
        days_back = analytics.get("dashboard_days_back")
        if days_back is not None:
            if not isinstance(days_back, int) or days_back < 1:
                errors.append("tracking.analytics.dashboard_days_back must be a positive integer")
        
        export_format = analytics.get("export_format")
        if export_format is not None:
            if not isinstance(export_format, str) or export_format not in ["json", "csv"]:
                errors.append("tracking.analytics.export_format must be 'json' or 'csv'")
    
    return errors


def migrate_tenant_to_tracking(tenant_config_path: str) -> bool:
    """
    Add default tracking configuration to an existing tenant config file.
    
    Args:
        tenant_config_path: Path to the tenant's config.json file
        
    Returns:
        True if migration was successful, False otherwise
    """
    try:
        import json
        
        # Read existing config
        with open(tenant_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Check if tracking config already exists
        if "tracking" in config.get("defaults", {}):
            print(f"ℹ️ Tracking configuration already exists in {tenant_config_path}")
            return True
        
        # Add tracking configuration
        if "defaults" not in config:
            config["defaults"] = {}
        
        config["defaults"]["tracking"] = get_default_tracking_config()["tracking"]
        
        # Write updated config
        with open(tenant_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Added tracking configuration to {tenant_config_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to migrate {tenant_config_path}: {e}")
        return False


def create_tracking_config_template() -> str:
    """
    Create a template showing all available tracking configuration options.
    
    Returns:
        JSON string with tracking configuration template
    """
    import json
    
    template = {
        "defaults": {
            "tracking": {
                "enabled": True,
                "database_path": None,
                "retention_days": 365,
                "analytics": {
                    "enabled": True,
                    "dashboard_days_back": 30,
                    "export_format": "json"
                }
            }
        }
    }
    
    return json.dumps(template, indent=2)

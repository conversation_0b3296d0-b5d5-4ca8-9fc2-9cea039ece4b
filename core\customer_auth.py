"""
Customer Authentication and Tenant Identification for Production
Replaces dev_mode logic with real customer authentication
"""

import jwt
import logging
from typing import Optional, Dict, Any
from flask import request
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class CustomerAuthManager:
    """
    Manages customer authentication and tenant identification in production
    """
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def generate_customer_token(self, customer_email: str, tenant_name: str, subscription_plan: str) -> str:
        """
        Generate JWT token for authenticated customer
        """
        payload = {
            'customer_email': customer_email,
            'tenant_name': tenant_name,
            'subscription_plan': subscription_plan,
            'issued_at': datetime.utcnow().isoformat(),
            'expires_at': (datetime.utcnow() + timedelta(hours=24)).isoformat()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def validate_customer_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Validate customer JWT token and return customer data
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # Check expiration
            expires_at = datetime.fromisoformat(payload['expires_at'])
            if datetime.utcnow() > expires_at:
                logger.warning("Customer token expired")
                return None
            
            return payload
            
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid customer token: {e}")
            return None
    
    def get_customer_from_request(self, request) -> Optional[Dict[str, Any]]:
        """
        Extract customer information from Flask request
        """
        # Check for Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            return self.validate_customer_token(token)
        
        # Check for dev_mode parameter (for development)
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'
        if dev_mode:
            return {
                'customer_email': '<EMAIL>',
                'tenant_name': 'prototype',
                'subscription_plan': 'business',
                'dev_mode': True
            }
        
        return None


def get_customer_tenant_name(request) -> Optional[str]:
    """
    Get tenant name for the current customer request
    Replaces the dev_mode logic throughout the application
    """
    # Initialize auth manager (in production, get secret from environment)
    auth_manager = CustomerAuthManager("your-secret-key-here")
    
    customer_data = auth_manager.get_customer_from_request(request)
    if customer_data:
        return customer_data['tenant_name']
    
    return None


def require_customer_auth(f):
    """
    Decorator to require customer authentication for API endpoints
    """
    def decorated_function(*args, **kwargs):
        tenant_name = get_customer_tenant_name(request)
        if not tenant_name:
            return {'error': 'Authentication required'}, 401
        
        # Add tenant_name to kwargs for the endpoint function
        kwargs['tenant_name'] = tenant_name
        return f(*args, **kwargs)
    
    decorated_function.__name__ = f.__name__
    return decorated_function


# Customer registration and login endpoints
def register_customer(email: str, company_name: str, subscription_plan: str = "starter") -> Dict[str, Any]:
    """
    Register new customer and create tenant
    """
    try:
        # Sanitize company name for tenant folder
        tenant_name = company_name.lower().replace(" ", "-").replace(".", "")
        
        # Generate onboarding link
        onboarding_url = f"https://yourdomain.com/onboard/{tenant_name}"
        
        # Store customer registration (implement database storage)
        customer_data = {
            'email': email,
            'company_name': company_name,
            'tenant_name': tenant_name,
            'subscription_plan': subscription_plan,
            'onboarding_url': onboarding_url,
            'status': 'pending_onboarding',
            'created_at': datetime.utcnow().isoformat()
        }
        
        # TODO: Store in database
        # store_customer_registration(customer_data)
        
        return {
            'success': True,
            'tenant_name': tenant_name,
            'onboarding_url': onboarding_url,
            'message': f'Registration successful. Please complete onboarding at: {onboarding_url}'
        }
        
    except Exception as e:
        logger.error(f"Customer registration failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }


def complete_customer_onboarding(tenant_name: str, azure_tenant_id: str) -> Dict[str, Any]:
    """
    Complete customer onboarding after Azure consent
    """
    try:
        # Update customer status
        # TODO: Update database
        # update_customer_status(tenant_name, 'active')
        
        # Initialize subscription
        from .subscription_manager import get_subscription_manager
        subscription_manager = get_subscription_manager(tenant_name)
        subscription_info = subscription_manager.get_subscription_info()
        
        # Generate customer access token
        auth_manager = CustomerAuthManager("your-secret-key-here")
        # TODO: Get customer email from database
        customer_email = f"admin@{tenant_name}.com"  # Placeholder
        
        access_token = auth_manager.generate_customer_token(
            customer_email, tenant_name, subscription_info['plan']
        )
        
        return {
            'success': True,
            'tenant_name': tenant_name,
            'access_token': access_token,
            'subscription': subscription_info,
            'dashboard_url': f'https://yourdomain.com/dashboard?token={access_token}'
        }
        
    except Exception as e:
        logger.error(f"Customer onboarding completion failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }

"""Analytics and reporting utilities for document processing tracking."""

import json
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import asdict

from .service import get_tracking_service
from .models import ProcessingStatistics, ProcessingEvent


class DocumentAnalytics:
    """Analytics service for generating reports and insights from document processing data."""
    
    def __init__(self, tenant_name: str):
        """
        Initialize analytics for a specific tenant.
        
        Args:
            tenant_name: Name of the tenant to analyze
        """
        self.tenant_name = tenant_name
        self.tracking_service = get_tracking_service()
    
    def get_dashboard_summary(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Get a comprehensive dashboard summary for the last N days.
        
        Args:
            days_back: Number of days to look back for statistics
            
        Returns:
            Dictionary containing dashboard data suitable for web display
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        # Get daily statistics for the period
        daily_stats = self.tracking_service.get_date_range_stats(
            self.tenant_name, start_date, end_date
        )
        
        # Calculate totals
        total_documents = sum(stat.total_documents for stat in daily_stats)
        total_successful = sum(stat.successful_documents for stat in daily_stats)
        total_failed = sum(stat.failed_documents for stat in daily_stats)
        total_file_size = sum(stat.total_file_size for stat in daily_stats)
        
        # Calculate success rate
        success_rate = (total_successful / total_documents * 100) if total_documents > 0 else 0
        
        # Aggregate document types
        doc_type_totals = {}
        for stat in daily_stats:
            for doc_type, count in stat.documents_by_type.items():
                doc_type_totals[doc_type] = doc_type_totals.get(doc_type, 0) + count
        
        # Get recent failed events for troubleshooting
        recent_failures = self.tracking_service.get_failed_events(self.tenant_name, days_back)
        
        # Calculate daily averages
        days_with_data = len([stat for stat in daily_stats if stat.total_documents > 0])
        avg_documents_per_day = total_documents / days_with_data if days_with_data > 0 else 0
        
        # Format file size in human-readable format
        def format_file_size(size_bytes: int) -> str:
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days_back
            },
            "totals": {
                "documents_processed": total_documents,
                "successful_documents": total_successful,
                "failed_documents": total_failed,
                "success_rate_percent": round(success_rate, 1),
                "total_file_size": total_file_size,
                "total_file_size_formatted": format_file_size(total_file_size)
            },
            "averages": {
                "documents_per_day": round(avg_documents_per_day, 1),
                "days_with_activity": days_with_data
            },
            "document_types": doc_type_totals,
            "recent_failures": [
                {
                    "filename": event.filename,
                    "document_type": event.document_type,
                    "error_message": event.error_message,
                    "processing_date": event.processing_date.isoformat(),
                    "mailbox_email": event.mailbox_email
                }
                for event in recent_failures[:10]  # Limit to 10 most recent
            ],
            "daily_trend": [
                {
                    "date": stat.period_start.isoformat(),
                    "total_documents": stat.total_documents,
                    "successful_documents": stat.successful_documents,
                    "failed_documents": stat.failed_documents,
                    "success_rate": round(stat.success_rate, 1)
                }
                for stat in daily_stats
            ]
        }
    
    def get_monthly_report(self, year: int, month: int) -> Dict[str, Any]:
        """
        Generate a detailed monthly report.
        
        Args:
            year: Year for the report
            month: Month for the report (1-12)
            
        Returns:
            Dictionary containing monthly report data
        """
        monthly_stats = self.tracking_service.get_monthly_stats(self.tenant_name, year, month)
        
        # Get daily breakdown for the month
        start_date = date(year, month, 1)
        from calendar import monthrange
        _, last_day = monthrange(year, month)
        end_date = date(year, month, last_day)
        
        daily_stats = self.tracking_service.get_date_range_stats(
            self.tenant_name, start_date, end_date
        )
        
        # Calculate trends
        daily_totals = [stat.total_documents for stat in daily_stats]
        peak_day = max(daily_totals) if daily_totals else 0
        avg_daily = sum(daily_totals) / len(daily_totals) if daily_totals else 0
        
        return {
            "period": {
                "year": year,
                "month": month,
                "month_name": start_date.strftime("%B"),
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "summary": asdict(monthly_stats),
            "daily_breakdown": [
                {
                    "date": stat.period_start.isoformat(),
                    "day_of_week": stat.period_start.strftime("%A"),
                    "total_documents": stat.total_documents,
                    "successful_documents": stat.successful_documents,
                    "failed_documents": stat.failed_documents,
                    "success_rate": round(stat.success_rate, 1),
                    "document_types": stat.documents_by_type
                }
                for stat in daily_stats
            ],
            "insights": {
                "peak_day_documents": peak_day,
                "average_daily_documents": round(avg_daily, 1),
                "most_common_document_type": max(
                    monthly_stats.documents_by_type.items(), 
                    key=lambda x: x[1]
                )[0] if monthly_stats.documents_by_type else None,
                "busiest_day_of_week": self._get_busiest_day_of_week(daily_stats)
            }
        }
    
    def get_yearly_report(self, year: int) -> Dict[str, Any]:
        """
        Generate a comprehensive yearly report.
        
        Args:
            year: Year for the report
            
        Returns:
            Dictionary containing yearly report data
        """
        yearly_stats = self.tracking_service.get_yearly_stats(self.tenant_name, year)
        
        # Get monthly breakdown
        monthly_reports = []
        for month in range(1, 13):
            monthly_stats = self.tracking_service.get_monthly_stats(self.tenant_name, year, month)
            monthly_reports.append({
                "month": month,
                "month_name": date(year, month, 1).strftime("%B"),
                "total_documents": monthly_stats.total_documents,
                "successful_documents": monthly_stats.successful_documents,
                "failed_documents": monthly_stats.failed_documents,
                "success_rate": round(monthly_stats.success_rate, 1),
                "document_types": monthly_stats.documents_by_type
            })
        
        # Calculate growth trends
        monthly_totals = [report["total_documents"] for report in monthly_reports]
        peak_month = max(monthly_totals) if monthly_totals else 0
        avg_monthly = sum(monthly_totals) / 12
        
        return {
            "period": {
                "year": year
            },
            "summary": asdict(yearly_stats),
            "monthly_breakdown": monthly_reports,
            "insights": {
                "peak_month_documents": peak_month,
                "average_monthly_documents": round(avg_monthly, 1),
                "total_growth": self._calculate_growth_trend(monthly_totals),
                "most_productive_month": monthly_reports[monthly_totals.index(max(monthly_totals))]["month_name"] if monthly_totals else None
            }
        }
    
    def get_document_type_analysis(self, days_back: int = 90) -> Dict[str, Any]:
        """
        Analyze document type distribution and trends.
        
        Args:
            days_back: Number of days to analyze
            
        Returns:
            Dictionary containing document type analysis
        """
        end_date = date.today()
        start_date = end_date - timedelta(days=days_back)
        
        daily_stats = self.tracking_service.get_date_range_stats(
            self.tenant_name, start_date, end_date
        )
        
        # Aggregate document types
        doc_type_stats = {}
        for stat in daily_stats:
            for doc_type, count in stat.documents_by_type.items():
                if doc_type not in doc_type_stats:
                    doc_type_stats[doc_type] = {
                        "total_count": 0,
                        "daily_counts": []
                    }
                doc_type_stats[doc_type]["total_count"] += count
        
        # Calculate percentages and trends
        total_documents = sum(stats["total_count"] for stats in doc_type_stats.values())
        
        for doc_type, stats in doc_type_stats.items():
            stats["percentage"] = round((stats["total_count"] / total_documents * 100), 1) if total_documents > 0 else 0
        
        # Sort by frequency
        sorted_types = sorted(
            doc_type_stats.items(), 
            key=lambda x: x[1]["total_count"], 
            reverse=True
        )
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days_back
            },
            "total_documents": total_documents,
            "document_types": {
                doc_type: {
                    "count": stats["total_count"],
                    "percentage": stats["percentage"]
                }
                for doc_type, stats in sorted_types
            },
            "top_document_type": sorted_types[0][0] if sorted_types else None
        }
    
    def _get_busiest_day_of_week(self, daily_stats: List[ProcessingStatistics]) -> str:
        """Calculate which day of the week is busiest."""
        day_totals = {}
        for stat in daily_stats:
            day_name = stat.period_start.strftime("%A")
            day_totals[day_name] = day_totals.get(day_name, 0) + stat.total_documents
        
        if not day_totals:
            return "No data"
        
        return max(day_totals.items(), key=lambda x: x[1])[0]
    
    def _calculate_growth_trend(self, monthly_totals: List[int]) -> str:
        """Calculate overall growth trend for the year."""
        if len(monthly_totals) < 2:
            return "Insufficient data"
        
        # Compare first half vs second half of year
        first_half = sum(monthly_totals[:6])
        second_half = sum(monthly_totals[6:])
        
        if first_half == 0:
            return "Growing" if second_half > 0 else "No activity"
        
        growth_rate = ((second_half - first_half) / first_half) * 100
        
        if growth_rate > 10:
            return f"Strong growth (+{growth_rate:.1f}%)"
        elif growth_rate > 0:
            return f"Moderate growth (+{growth_rate:.1f}%)"
        elif growth_rate > -10:
            return f"Slight decline ({growth_rate:.1f}%)"
        else:
            return f"Significant decline ({growth_rate:.1f}%)"


def export_analytics_to_json(tenant_name: str, output_file: str, days_back: int = 30) -> bool:
    """
    Export analytics data to a JSON file for external consumption.
    
    Args:
        tenant_name: Name of the tenant
        output_file: Path to output JSON file
        days_back: Number of days to include in the export
        
    Returns:
        True if export was successful, False otherwise
    """
    try:
        analytics = DocumentAnalytics(tenant_name)
        
        export_data = {
            "tenant_name": tenant_name,
            "export_date": datetime.now().isoformat(),
            "dashboard_summary": analytics.get_dashboard_summary(days_back),
            "document_type_analysis": analytics.get_document_type_analysis(days_back)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"❌ Failed to export analytics: {e}")
        return False

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'customer' | 'admin';
  tenantId: string;
  subscription?: {
    plan: 'starter' | 'business';
    status: 'active' | 'canceled' | 'past_due';
    nextBilling: string;
  };
}

export interface Tenant {
  id: string;
  name: string;
  email: string;
  plan: 'starter' | 'business';
  status: 'active' | 'inactive' | 'suspended';
  documentsProcessed: number;
  mailboxCount: number;
  joinDate: string;
  lastActivity: string;
}

export interface Document {
  id: string;
  filename: string;
  type: 'invoice' | 'certificate' | 'custom';
  status: 'processing' | 'completed' | 'failed';
  processedAt: string;
  mailboxId: string;
  size: number;
  extractedData?: Record<string, any>;
}

export interface Mailbox {
  id: string;
  email: string;
  name: string;
  isActive: boolean;
  documentTypes: string[];
  processedCount: number;
  lastProcessed?: string;
}

export interface ProcessingStats {
  totalDocuments: number;
  successRate: number;
  averageProcessingTime: number;
  dailyStats: Array<{
    date: string;
    processed: number;
    success: number;
    failed: number;
  }>;
  monthlyStats: Array<{
    month: string;
    processed: number;
    success: number;
    failed: number;
  }>;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: string;
  processingQueue: number;
  errorRate: number;
  activeUsers: number;
  serverLoad: number;
}

export interface ErrorLog {
  id: string;
  timestamp: string;
  level: 'error' | 'warning' | 'info';
  message: string;
  source: string;
  userId?: string;
  tenantId?: string;
  details?: Record<string, any>;
}